import { Request, Response, NextFunction } from "express";
import { JobType, UserRole, UserStatus } from "@prisma/client";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";

// Use type assertion instead of interface extension to avoid conflicts
type AuthenticatedRequest = Request & {
  user?: {
    userId: string;
    email: string;
    role: UserRole;
    status: UserStatus;
    id: string;
  };
};

interface CreateJobRequest {
  title: string;
  company: string;
  location: string;
  type: JobType;
  description: string;
  requirements?: string;
  salary?: string;
  applicationUrl?: string;
  allowResume: boolean;
  relevantCourses?: string[];
  expiresAt?: string;
}

interface UpdateJobRequest {
  title?: string;
  company?: string;
  location?: string;
  type?: JobType;
  description?: string;
  requirements?: string;
  salary?: string;
  applicationUrl?: string;
  allowResume?: boolean;
  relevantCourses?: string[];
  expiresAt?: string;
  isActive?: boolean;
}

interface JobApplicationRequest {
  resumeUrl?: string;
  message?: string;
}

interface ApplicationStatusRequest {
  status: "PENDING" | "REVIEWED" | "ACCEPTED" | "REJECTED";
}

/**
 * Create a new job posting
 */
export const createJob = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const {
      title,
      company,
      location,
      type,
      description,
      requirements,
      salary,
      applicationUrl,
      allowResume,
      relevantCourses,
      expiresAt,
    } = req.body as CreateJobRequest;

    // Only alumni and admins can post jobs
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true, status: true },
    });

    if (!user || user.status !== UserStatus.APPROVED) {
      throw createError("User not found or not approved", 403);
    }

    if (user.role !== UserRole.ALUMNI && user.role !== UserRole.ADMIN) {
      throw createError("Only alumni and admins can post jobs", 403);
    }

    // Create job
    const job = await prisma.job.create({
      data: {
        title,
        company,
        location,
        type,
        description,
        requirements: requirements ?? null,
        salary: salary ?? null,
        applicationUrl: applicationUrl ?? null,
        allowResume,
        relevantCourses: relevantCourses ? (JSON.stringify(relevantCourses) as any) : null,
        expiresAt: expiresAt ? new Date(expiresAt) : null,
        postedById: req.user.userId,
      },
      include: {
        postedBy: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
          },
        },
        _count: {
          select: {
            applications: true,
          },
        },
      },
    });

    res.status(201).json({
      message: "Job posted successfully",
      job: {
        ...job,
        relevantCourses: job.relevantCourses ? JSON.parse(job.relevantCourses as string) : null,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all jobs with filtering and pagination
 */
export const getJobs = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    // Filtering options
    const type = req.query.type as JobType;
    const location = req.query.location as string;
    const company = req.query.company as string;
    const search = req.query.search as string;
    const course = req.query.course as string;

    // Build where clause
    const where: any = {
      isActive: true,
      OR: [{ expiresAt: null }, { expiresAt: { gt: new Date() } }],
    };

    if (type) {
      where.type = type;
    }

    if (location) {
      where.location = { contains: location, mode: "insensitive" };
    }

    if (company) {
      where.company = { contains: company, mode: "insensitive" };
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { company: { contains: search, mode: "insensitive" } },
      ];
    }

    if (course) {
      where.relevantCourses = { contains: course };
    }

    const [jobs, total] = await Promise.all([
      prisma.job.findMany({
        where,
        include: {
          postedBy: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
              company: true,
              jobTitle: true,
            },
          },
          _count: {
            select: {
              applications: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.job.count({ where }),
    ]);

    // Parse relevantCourses JSON
    const formattedJobs = jobs.map((job) => ({
      ...job,
      relevantCourses: job.relevantCourses ? JSON.parse(job.relevantCourses as string) : null,
    }));

    res.json({
      jobs: formattedJobs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get job by ID
 */
export const getJobById = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const job = await prisma.job.findUnique({
      where: { id: id as string },
      include: {
        postedBy: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
            linkedinUrl: true,
          },
        },
        _count: {
          select: {
            applications: true,
          },
        },
      },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    if (!job.isActive) {
      throw createError("Job is no longer active", 410);
    }

    // Check if job has expired
    if (job.expiresAt && job.expiresAt < new Date()) {
      throw createError("Job has expired", 410);
    }

    res.json({
      job: {
        ...job,
        relevantCourses: job.relevantCourses ? JSON.parse(job.relevantCourses as string) : null,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update job (only by job poster or admin)
 */
export const updateJob = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;
    const updateData = req.body as UpdateJobRequest;

    // Find job and check ownership
    const job = await prisma.job.findUnique({
      where: { id: id as string },
      select: { postedById: true },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    // Check if user is the job poster or admin
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    if (job.postedById !== req.user.userId && user.role !== UserRole.ADMIN) {
      throw createError("Not authorized to update this job", 403);
    }

    // Prepare update data
    const cleanedData: any = Object.fromEntries(Object.entries(updateData).filter(([_, value]) => value !== undefined));

    if (cleanedData.relevantCourses) {
      cleanedData.relevantCourses = JSON.stringify(cleanedData.relevantCourses);
    }

    if (cleanedData.expiresAt) {
      cleanedData.expiresAt = new Date(cleanedData.expiresAt);
    }

    const updatedJob = await prisma.job.update({
      where: { id: id as string },
      data: cleanedData,
      include: {
        postedBy: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
          },
        },
        _count: {
          select: {
            applications: true,
          },
        },
      },
    });

    res.json({
      message: "Job updated successfully",
      job: {
        ...updatedJob,
        relevantCourses: updatedJob.relevantCourses ? JSON.parse(updatedJob.relevantCourses as string) : null,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete job (only by job poster or admin)
 */
export const deleteJob = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;

    // Find job and check ownership
    const job = await prisma.job.findUnique({
      where: { id: id as string },
      select: { postedById: true, title: true },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    // Check if user is the job poster or admin
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    if (job.postedById !== req.user.userId && user.role !== UserRole.ADMIN) {
      throw createError("Not authorized to delete this job", 403);
    }

    // Delete job (this will cascade delete applications)
    await prisma.job.delete({
      where: { id: id as string },
    });

    res.json({
      message: "Job deleted successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Apply for a job
 */
export const applyForJob = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;
    const { resumeUrl, message } = req.body as JobApplicationRequest;

    // Check if job exists and is active
    const job = await prisma.job.findUnique({
      where: { id: id as string },
      select: {
        id: true,
        title: true,
        company: true,
        isActive: true,
        expiresAt: true,
        allowResume: true,
        postedById: true,
      },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    if (!job.isActive) {
      throw createError("Job is no longer active", 410);
    }

    if (job.expiresAt && job.expiresAt < new Date()) {
      throw createError("Job has expired", 410);
    }

    // Check if user is trying to apply to their own job
    if (job.postedById === req.user.userId) {
      throw createError("Cannot apply to your own job posting", 400);
    }

    // Check if user has already applied
    const existingApplication = await prisma.jobApplication.findUnique({
      where: {
        jobId_applicantId: {
          jobId: id as string,
          applicantId: req.user.userId,
        },
      },
    });

    if (existingApplication) {
      throw createError("You have already applied for this job", 409);
    }

    // Validate resume requirement
    if (job.allowResume && !resumeUrl) {
      throw createError("Resume is required for this job application", 400);
    }

    // Create application
    const application = await prisma.jobApplication.create({
      data: {
        jobId: id as string,
        applicantId: req.user.userId,
        resumeUrl: resumeUrl ?? null,
        message: message ?? null,
      },
      include: {
        job: {
          select: {
            title: true,
            company: true,
          },
        },
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
            profilePicture: true,
          },
        },
      },
    });

    res.status(201).json({
      message: "Application submitted successfully",
      application: {
        id: application.id,
        job: {
          title: job.title,
          company: job.company,
        },
        status: application.status,
        createdAt: application.createdAt,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get job applications for a specific job (only job poster or admin)
 */
export const getJobApplications = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;
    const status = req.query.status as string;

    // Check if job exists and user has permission
    const job = await prisma.job.findUnique({
      where: { id: id as string },
      select: { postedById: true, title: true, company: true },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    // Check if user is the job poster or admin
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    if (job.postedById !== req.user.userId && user.role !== UserRole.ADMIN) {
      throw createError("Not authorized to view applications for this job", 403);
    }

    // Build where clause
    const where: any = { jobId: id };
    if (status) {
      where.status = status;
    }

    const [applications, total] = await Promise.all([
      prisma.jobApplication.findMany({
        where,
        include: {
          applicant: {
            select: {
              id: true,
              name: true,
              email: true,
              profilePicture: true,
              course: true,
              linkedinUrl: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.jobApplication.count({ where }),
    ]);

    res.json({
      job: {
        id,
        title: job.title,
        company: job.company,
      },
      applications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update application status (only job poster or admin)
 */
export const updateApplicationStatus = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { jobId, applicationId } = req.params;
    const { status } = req.body as ApplicationStatusRequest;

    // Check if job exists and user has permission
    const job = await prisma.job.findUnique({
      where: { id: jobId as string },
      select: { postedById: true },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    // Check if user is the job poster or admin
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    if (job.postedById !== req.user.userId && user.role !== UserRole.ADMIN) {
      throw createError("Not authorized to update application status", 403);
    }

    // Check if application exists
    const application = await prisma.jobApplication.findUnique({
      where: { id: applicationId as string },
      select: { jobId: true },
    });

    if (!application) {
      throw createError("Application not found", 404);
    }

    if (application.jobId !== jobId) {
      throw createError("Application does not belong to this job", 400);
    }

    // Update application status
    const updatedApplication = await prisma.jobApplication.update({
      where: { id: applicationId as string },
      data: { status },
      include: {
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
            profilePicture: true,
          },
        },
        job: {
          select: {
            title: true,
            company: true,
          },
        },
      },
    });

    res.json({
      message: "Application status updated successfully",
      application: updatedApplication,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get user's job applications
 */
export const getMyApplications = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;
    const status = req.query.status as string;

    // Build where clause
    const where: any = { applicantId: req.user.userId };
    if (status) {
      where.status = status;
    }

    const [applications, total] = await Promise.all([
      prisma.jobApplication.findMany({
        where,
        include: {
          job: {
            select: {
              id: true,
              title: true,
              company: true,
              location: true,
              type: true,
              isActive: true,
              expiresAt: true,
              postedBy: {
                select: {
                  name: true,
                  profilePicture: true,
                },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.jobApplication.count({ where }),
    ]);

    res.json({
      applications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
