import { body, param, query, validationResult } from "express-validator";
import { Request, Response, NextFunction } from "express";
import { UserRole } from "@prisma/client";
import { AuthUtils } from "../utils/auth";
import { createError } from "./errorHandler";

/**
 * Middleware to handle validation errors
 */
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map((error) => ({
      field: error.type === "field" ? error.path : "unknown",
      message: error.msg,
      value: error.type === "field" ? error.value : undefined,
    }));

    res.status(400).json({
      error: "Validation failed",
      details: errorMessages,
      timestamp: new Date().toISOString(),
    });
    return;
  }
  next();
};

// Auth validation schemas
export const registerValidation = [
  body("email").isEmail().normalizeEmail().withMessage("Please provide a valid email address"),

  body("password")
    .isLength({ min: 8 })
    .withMessage("Password must be at least 8 characters long")
    .custom((password) => {
      const validation = AuthUtils.validatePassword(password);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(", "));
      }
      return true;
    }),

  body("name").trim().isLength({ min: 2, max: 100 }).withMessage("Name must be between 2 and 100 characters"),

  body("mobile").optional().isMobilePhone("any").withMessage("Please provide a valid mobile number"),

  body("usn")
    .trim()
    .isLength({ min: 6, max: 20 })
    .withMessage("USN must be between 6 and 20 characters")
    .custom((usn) => {
      if (!AuthUtils.validateUSN(usn)) {
        throw new Error("Invalid USN format");
      }
      return true;
    }),

  body("course").trim().isLength({ min: 2, max: 100 }).withMessage("Course must be between 2 and 100 characters"),

  body("batch").trim().isLength({ min: 4, max: 4 }).isNumeric().withMessage("Batch must be a 4-digit year"),

  body("role").isIn([UserRole.STUDENT, UserRole.ALUMNI]).withMessage("Role must be either STUDENT or ALUMNI"),

  handleValidationErrors,
];

export const loginValidation = [
  body("email").isEmail().normalizeEmail().withMessage("Please provide a valid email address"),

  body("password").notEmpty().withMessage("Password is required"),

  handleValidationErrors,
];

export const refreshTokenValidation = [
  body("refreshToken").notEmpty().withMessage("Refresh token is required"),

  handleValidationErrors,
];

// Profile validation schemas
export const updateProfileValidation = [
  body("name")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Name must be between 2 and 100 characters"),

  body("bio").optional().trim().isLength({ max: 500 }).withMessage("Bio must not exceed 500 characters"),

  body("mobile").optional().isMobilePhone("any").withMessage("Please provide a valid mobile number"),

  body("linkedinUrl").optional().isURL().withMessage("Please provide a valid LinkedIn URL"),

  body("githubUrl").optional().isURL().withMessage("Please provide a valid GitHub URL"),

  body("portfolioUrl").optional().isURL().withMessage("Please provide a valid portfolio URL"),

  body("company").optional().trim().isLength({ max: 100 }).withMessage("Company name must not exceed 100 characters"),

  body("jobTitle").optional().trim().isLength({ max: 100 }).withMessage("Job title must not exceed 100 characters"),

  body("experience").optional().isInt({ min: 0, max: 50 }).withMessage("Experience must be between 0 and 50 years"),

  body("location").optional().trim().isLength({ max: 100 }).withMessage("Location must not exceed 100 characters"),

  handleValidationErrors,
];

// Job validation schemas
export const createJobValidation = [
  body("title").trim().isLength({ min: 5, max: 200 }).withMessage("Job title must be between 5 and 200 characters"),

  body("company")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Company name must be between 2 and 100 characters"),

  body("location").trim().isLength({ min: 2, max: 100 }).withMessage("Location must be between 2 and 100 characters"),

  body("type").isIn(["FULL_TIME", "PART_TIME", "INTERNSHIP", "CONTRACT", "FREELANCE"]).withMessage("Invalid job type"),

  body("description")
    .trim()
    .isLength({ min: 50, max: 2000 })
    .withMessage("Description must be between 50 and 2000 characters"),

  body("requirements")
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage("Requirements must not exceed 1000 characters"),

  body("salary").optional().trim().isLength({ max: 100 }).withMessage("Salary must not exceed 100 characters"),

  body("applicationUrl").optional().isURL().withMessage("Please provide a valid application URL"),

  body("relevantCourses").isArray({ min: 1 }).withMessage("At least one relevant course must be specified"),

  body("relevantCourses.*")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Each course name must be between 2 and 100 characters"),

  handleValidationErrors,
];

export const updateJobValidation = [
  body("title")
    .optional()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage("Job title must be between 5 and 200 characters"),

  body("company")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Company name must be between 2 and 100 characters"),

  body("location")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Location must be between 2 and 100 characters"),

  body("type")
    .optional()
    .isIn(["FULL_TIME", "PART_TIME", "INTERNSHIP", "CONTRACT", "FREELANCE"])
    .withMessage("Invalid job type"),

  body("description")
    .optional()
    .trim()
    .isLength({ min: 50, max: 2000 })
    .withMessage("Description must be between 50 and 2000 characters"),

  body("requirements")
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage("Requirements must not exceed 1000 characters"),

  body("salary").optional().trim().isLength({ max: 100 }).withMessage("Salary must not exceed 100 characters"),

  body("applicationUrl").optional().isURL().withMessage("Please provide a valid application URL"),

  body("allowResume").optional().isBoolean().withMessage("allowResume must be a boolean"),

  body("relevantCourses").optional().isArray().withMessage("relevantCourses must be an array"),

  body("relevantCourses.*")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Each course name must be between 2 and 100 characters"),

  body("expiresAt").optional().isISO8601().withMessage("Please provide a valid expiration date"),

  body("isActive").optional().isBoolean().withMessage("isActive must be a boolean"),

  handleValidationErrors,
];

// Event validation schemas
export const createEventValidation = [
  body("title").trim().isLength({ min: 5, max: 200 }).withMessage("Event title must be between 5 and 200 characters"),

  body("description")
    .trim()
    .isLength({ min: 20, max: 2000 })
    .withMessage("Description must be between 20 and 2000 characters"),

  body("location").optional().trim().isLength({ max: 200 }).withMessage("Location must not exceed 200 characters"),

  body("startTime").isISO8601().withMessage("Please provide a valid start time"),

  body("endTime").optional().isISO8601().withMessage("Please provide a valid end time"),

  body("maxAttendees")
    .optional()
    .isInt({ min: 1, max: 10000 })
    .withMessage("Max attendees must be between 1 and 10000"),

  handleValidationErrors,
];

export const updateEventValidation = [
  body("title")
    .optional()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage("Event title must be between 5 and 200 characters"),

  body("description")
    .optional()
    .trim()
    .isLength({ min: 20, max: 2000 })
    .withMessage("Description must be between 20 and 2000 characters"),

  body("location").optional().trim().isLength({ max: 200 }).withMessage("Location must not exceed 200 characters"),

  body("startTime").optional().isISO8601().withMessage("Please provide a valid start time"),

  body("endTime").optional().isISO8601().withMessage("Please provide a valid end time"),

  body("isOnline").optional().isBoolean().withMessage("isOnline must be a boolean"),

  body("meetingUrl").optional().isURL().withMessage("Please provide a valid meeting URL"),

  body("maxAttendees")
    .optional()
    .isInt({ min: 1, max: 10000 })
    .withMessage("Max attendees must be between 1 and 10000"),

  body("isActive").optional().isBoolean().withMessage("isActive must be a boolean"),

  body("imageUrl").optional().isURL().withMessage("Please provide a valid image URL"),

  handleValidationErrors,
];

export const rsvpValidation = [
  body("status").isIn(["GOING", "MAYBE", "NOT_GOING"]).withMessage("Status must be one of: GOING, MAYBE, NOT_GOING"),

  handleValidationErrors,
];

// Common validation schemas
export const idValidation = [param("id").isLength({ min: 1 }).withMessage("ID is required"), handleValidationErrors];

export const paginationValidation = [
  query("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),

  query("limit").optional().isInt({ min: 1, max: 100 }).withMessage("Limit must be between 1 and 100"),

  handleValidationErrors,
];

// Connection validation schemas
export const connectionRequestValidation = [
  body("receiverId").notEmpty().withMessage("Receiver ID is required"),

  body("message").optional().trim().isLength({ max: 500 }).withMessage("Message must not exceed 500 characters"),

  handleValidationErrors,
];

export const connectionResponseValidation = [
  body("status").isIn(["ACCEPTED", "REJECTED", "BLOCKED"]).withMessage("Status must be ACCEPTED, REJECTED, or BLOCKED"),

  handleValidationErrors,
];

// Job application validation schemas
export const jobApplicationValidation = [
  body("resumeUrl").optional().isURL().withMessage("Please provide a valid resume URL"),

  body("message").optional().trim().isLength({ max: 1000 }).withMessage("Message must not exceed 1000 characters"),

  handleValidationErrors,
];

export const applicationStatusValidation = [
  body("status")
    .isIn(["PENDING", "REVIEWED", "ACCEPTED", "REJECTED"])
    .withMessage("Status must be PENDING, REVIEWED, ACCEPTED, or REJECTED"),

  handleValidationErrors,
];

// Event RSVP validation schemas (already defined above)

// Post validation schemas
export const createPostValidation = [
  body("title").trim().isLength({ min: 5, max: 200 }).withMessage("Title must be between 5 and 200 characters"),

  body("content").trim().isLength({ min: 10, max: 5000 }).withMessage("Content must be between 10 and 5000 characters"),

  body("type").isIn(["ADVICE", "GENERAL", "ANNOUNCEMENT"]).withMessage("Type must be ADVICE, GENERAL, or ANNOUNCEMENT"),

  body("isPublic").isBoolean().withMessage("isPublic must be a boolean"),

  body("imageUrl").optional().isURL().withMessage("Please provide a valid image URL"),

  handleValidationErrors,
];

// Message validation schemas
export const sendMessageValidation = [
  body("receiverId").notEmpty().withMessage("Receiver ID is required"),

  body("content")
    .trim()
    .isLength({ min: 1, max: 2000 })
    .withMessage("Message content must be between 1 and 2000 characters"),

  handleValidationErrors,
];

// Notification preferences validation
export const notificationPreferencesValidation = [
  body("emailJobPosted").optional().isBoolean().withMessage("emailJobPosted must be a boolean"),
  body("emailEventCreated").optional().isBoolean().withMessage("emailEventCreated must be a boolean"),
  body("emailMessageReceived").optional().isBoolean().withMessage("emailMessageReceived must be a boolean"),
  body("emailConnectionRequest").optional().isBoolean().withMessage("emailConnectionRequest must be a boolean"),
  body("emailPostCreated").optional().isBoolean().withMessage("emailPostCreated must be a boolean"),
  body("emailSystemUpdates").optional().isBoolean().withMessage("emailSystemUpdates must be a boolean"),
  body("inAppJobPosted").optional().isBoolean().withMessage("inAppJobPosted must be a boolean"),
  body("inAppEventCreated").optional().isBoolean().withMessage("inAppEventCreated must be a boolean"),
  body("inAppMessageReceived").optional().isBoolean().withMessage("inAppMessageReceived must be a boolean"),
  body("inAppConnectionRequest").optional().isBoolean().withMessage("inAppConnectionRequest must be a boolean"),
  body("inAppPostCreated").optional().isBoolean().withMessage("inAppPostCreated must be a boolean"),
  body("inAppSystemUpdates").optional().isBoolean().withMessage("inAppSystemUpdates must be a boolean"),
  body("emailDigest").optional().isBoolean().withMessage("emailDigest must be a boolean"),
  body("emailDigestFrequency")
    .optional()
    .isIn(["DAILY", "WEEKLY", "MONTHLY"])
    .withMessage("emailDigestFrequency must be DAILY, WEEKLY, or MONTHLY"),

  handleValidationErrors,
];
