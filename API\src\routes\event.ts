import { Router } from "express";
import { authenticate, requireApproved, requireAlumniOrAdmin, optionalAuth } from "../middleware/auth";
import {
  createEventValidation,
  updateEventValidation,
  rsvpValidation,
  paginationValidation,
  idValidation,
} from "../middleware/validation";
import * as eventController from "../controllers/eventController";

const router = Router();

// Get user's own event RSVPs (must come before /:id routes)
router.get("/rsvps/my", authenticate, requireApproved, paginationValidation, eventController.getMyRSVPs);

// Get all events (with filtering and pagination) - optional auth to show RSVP status
router.get("/", optionalAuth, paginationValidation, eventController.getEvents);

// Create new event (alumni and admin only)
router.post("/", authenticate, requireAlumniOrAdmin, createEventValidation, eventController.createEvent);

// Get event by ID - optional auth to show RSVP status
router.get("/:id", optionalAuth, idValidation, eventController.getEventById);

// Update event (only event organizer or admin)
router.put("/:id", authenticate, requireApproved, idValidation, updateEventValidation, eventController.updateEvent);

// Delete event (only event organizer or admin)
router.delete("/:id", authenticate, requireApproved, idValidation, eventController.deleteEvent);

// RSVP to an event
router.post("/:id/rsvp", authenticate, requireApproved, idValidation, rsvpValidation, eventController.rsvpToEvent);

export default router;
