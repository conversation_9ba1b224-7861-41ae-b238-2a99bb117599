import { Router } from "express";
import { authenticate, requireApproved, requireAlumniOrAdmin } from "../middleware/auth";
import {
  createJobValidation,
  updateJobValidation,
  jobApplicationValidation,
  applicationStatusValidation,
  paginationValidation,
  idValidation,
} from "../middleware/validation";
import * as jobController from "../controllers/jobController";

const router = Router();

// All routes require authentication
router.use(authenticate);

// Get user's own job applications (must come before /:id routes)
router.get("/applications/my", requireApproved, paginationValidation, jobController.getMyApplications);

// Get all jobs (with filtering and pagination)
router.get("/", requireApproved, paginationValidation, jobController.getJobs);

// Create new job (alumni and admin only)
router.post("/", requireAlumniOrAdmin, createJobValidation, jobController.createJob);

// Get job by ID
router.get("/:id", requireApproved, idValidation, jobController.getJobById);

// Update job (only job poster or admin)
router.put("/:id", requireApproved, idValidation, updateJobValidation, jobController.updateJob);

// Delete job (only job poster or admin)
router.delete("/:id", requireApproved, idValidation, jobController.deleteJob);

// Apply for a job
router.post("/:id/apply", requireApproved, idValidation, jobApplicationValidation, jobController.applyForJob);

// Get applications for a specific job (only job poster or admin)
router.get("/:id/applications", requireApproved, idValidation, paginationValidation, jobController.getJobApplications);

// Update application status (only job poster or admin)
router.put(
  "/:jobId/applications/:applicationId/status",
  requireApproved,
  applicationStatusValidation,
  jobController.updateApplicationStatus
);

export default router;
