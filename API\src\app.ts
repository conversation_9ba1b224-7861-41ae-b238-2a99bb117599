import cookieParser from "cookie-parser";
import cors from "cors";
import dotenv from "dotenv";
import express from "express";
import helmet from "helmet";
import { createServer } from "http";
import morgan from "morgan";
import { WebSocketServer } from "ws";

// Load environment variables
dotenv.config();

// Import configurations and middleware
import { corsOptions } from "./config/cors";
import { errorHandler } from "./middleware/errorHandler";
import { notFoundHandler } from "./middleware/notFoundHandler";
import { rateLimiter } from "./middleware/rateLimiter";

// Import routes
import authRoutes from "./routes/auth";
import userRoutes from "./routes/user";
import jobRoutes from "./routes/job";
import eventRoutes from "./routes/event";

// Import WebSocket handler
import { setupWebSocket } from "./services/websocket";

const app = express();
const PORT = parseInt(process.env.PORT || "3000");
const WS_PORT = parseInt(process.env.WS_PORT || "3001");

// Create HTTP server
const server = createServer(app);

// Security middleware
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
  })
);

// CORS configuration
app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Cookie parser
app.use(cookieParser());

// Logging middleware
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
} else {
  app.use(morgan("combined"));
}

// Rate limiting
app.use(rateLimiter);

// Performance monitoring middleware
import { performanceHeaders, performanceMonitoring } from "./middleware/performance";
app.use(performanceHeaders());
app.use(performanceMonitoring());

// API versioning and optimization middleware
import {
  cacheHeaders,
  fieldFiltering,
  paginationOptimization,
  responseCompression,
  responseTransformation,
} from "./middleware/responseOptimization";
import { apiVersioning, versionAnalytics } from "./middleware/versioning";

app.use(apiVersioning());
app.use(versionAnalytics());
app.use(fieldFiltering());
app.use(responseCompression());
app.use(responseTransformation());
app.use(paginationOptimization());
app.use(cacheHeaders({ maxAge: 300, private: false }));

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    message: "Alumni Portal API is running",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
  });
});

// API routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/jobs", jobRoutes);
app.use("/api/events", eventRoutes);

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Start HTTP server
server.listen(PORT, () => {
  console.log(`🚀 Alumni Portal API server running on port http://localhost:${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
});

// Setup WebSocket server
const wss = new WebSocketServer({ port: WS_PORT });
setupWebSocket(wss);

console.log(`🔌 WebSocket server running on port ${WS_PORT}`);

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("SIGTERM received, shutting down gracefully");
  server.close(() => {
    console.log("HTTP server closed");
    wss.close(() => {
      console.log("WebSocket server closed");
      process.exit(0);
    });
  });
});

process.on("SIGINT", () => {
  console.log("SIGINT received, shutting down gracefully");
  server.close(() => {
    console.log("HTTP server closed");
    wss.close(() => {
      console.log("WebSocket server closed");
      process.exit(0);
    });
  });
});

export default app;
