import { Request, Response, NextFunction } from "express";
import { JobType, UserRole, UserStatus } from "@prisma/client";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";

// Use type assertion instead of interface extension to avoid conflicts
type AuthenticatedRequest = Request & {
  user?: {
    userId: string;
    email: string;
    role: UserRole;
    status: UserStatus;
    id: string;
  };
};

interface CreateJobRequest {
  title: string;
  company: string;
  location: string;
  type: JobType;
  description: string;
  requirements?: string;
  salary?: string;
  applicationUrl?: string;
  allowResume: boolean;
  relevantCourses?: string[];
  expiresAt?: string;
}

interface UpdateJobRequest {
  title?: string;
  company?: string;
  location?: string;
  type?: JobType;
  description?: string;
  requirements?: string;
  salary?: string;
  applicationUrl?: string;
  allowResume?: boolean;
  relevantCourses?: string[];
  expiresAt?: string;
  isActive?: boolean;
}

interface JobApplicationRequest {
  resumeUrl?: string;
  message?: string;
}

interface ApplicationStatusRequest {
  status: "PENDING" | "REVIEWED" | "ACCEPTED" | "REJECTED";
}

/**
 * Create a new job posting
 */
export const createJob = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const {
      title,
      company,
      location,
      type,
      description,
      requirements,
      salary,
      applicationUrl,
      allowResume,
      relevantCourses,
      expiresAt,
    } = req.body as CreateJobRequest;

    // Only alumni and admins can post jobs
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true, status: true },
    });

    if (!user || user.status !== UserStatus.APPROVED) {
      throw createError("User not found or not approved", 403);
    }

    if (user.role !== UserRole.ALUMNI && user.role !== UserRole.ADMIN) {
      throw createError("Only alumni and admins can post jobs", 403);
    }

    // Create job
    const job = await prisma.job.create({
      data: {
        title,
        company,
        location,
        type,
        description,
        requirements: requirements ?? null,
        salary: salary ?? null,
        applicationUrl: applicationUrl ?? null,
        allowResume,
        relevantCourses: relevantCourses ? (JSON.stringify(relevantCourses) as any) : null,
        expiresAt: expiresAt ? new Date(expiresAt) : null,
        postedById: req.user.userId,
      },
      include: {
        postedBy: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
          },
        },
        _count: {
          select: {
            applications: true,
          },
        },
      },
    });

    res.status(201).json({
      message: "Job posted successfully",
      job: {
        ...job,
        relevantCourses: job.relevantCourses ? JSON.parse(job.relevantCourses as string) : null,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all jobs with filtering and pagination
 */
export const getJobs = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    // Filtering options
    const type = req.query.type as JobType;
    const location = req.query.location as string;
    const company = req.query.company as string;
    const search = req.query.search as string;
    const course = req.query.course as string;

    // Build where clause
    const where: any = {
      isActive: true,
      OR: [{ expiresAt: null }, { expiresAt: { gt: new Date() } }],
    };

    if (type) {
      where.type = type;
    }

    if (location) {
      where.location = { contains: location, mode: "insensitive" };
    }

    if (company) {
      where.company = { contains: company, mode: "insensitive" };
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { company: { contains: search, mode: "insensitive" } },
      ];
    }

    if (course) {
      where.relevantCourses = { contains: course };
    }

    const [jobs, total] = await Promise.all([
      prisma.job.findMany({
        where,
        include: {
          postedBy: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
              company: true,
              jobTitle: true,
            },
          },
          _count: {
            select: {
              applications: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.job.count({ where }),
    ]);

    // Parse relevantCourses JSON
    const formattedJobs = jobs.map((job) => ({
      ...job,
      relevantCourses: job.relevantCourses ? JSON.parse(job.relevantCourses as string) : null,
    }));

    res.json({
      jobs: formattedJobs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get job by ID
 */
export const getJobById = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const job = await prisma.job.findUnique({
      where: { id: id as string },
      include: {
        postedBy: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
            linkedinUrl: true,
          },
        },
        _count: {
          select: {
            applications: true,
          },
        },
      },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    if (!job.isActive) {
      throw createError("Job is no longer active", 410);
    }

    // Check if job has expired
    if (job.expiresAt && job.expiresAt < new Date()) {
      throw createError("Job has expired", 410);
    }

    res.json({
      job: {
        ...job,
        relevantCourses: job.relevantCourses ? JSON.parse(job.relevantCourses as string) : null,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update job (only by job poster or admin)
 */
export const updateJob = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;
    const updateData = req.body as UpdateJobRequest;

    // Find job and check ownership
    const job = await prisma.job.findUnique({
      where: { id: id as string },
      select: { postedById: true },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    // Check if user is the job poster or admin
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    if (job.postedById !== req.user.userId && user.role !== UserRole.ADMIN) {
      throw createError("Not authorized to update this job", 403);
    }

    // Prepare update data
    const cleanedData: any = Object.fromEntries(Object.entries(updateData).filter(([_, value]) => value !== undefined));

    if (cleanedData.relevantCourses) {
      cleanedData.relevantCourses = JSON.stringify(cleanedData.relevantCourses);
    }

    if (cleanedData.expiresAt) {
      cleanedData.expiresAt = new Date(cleanedData.expiresAt);
    }

    const updatedJob = await prisma.job.update({
      where: { id: id as string },
      data: cleanedData,
      include: {
        postedBy: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
          },
        },
        _count: {
          select: {
            applications: true,
          },
        },
      },
    });

    res.json({
      message: "Job updated successfully",
      job: {
        ...updatedJob,
        relevantCourses: updatedJob.relevantCourses ? JSON.parse(updatedJob.relevantCourses as string) : null,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete job (only by job poster or admin)
 */
export const deleteJob = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;

    // Find job and check ownership
    const job = await prisma.job.findUnique({
      where: { id: id as string },
      select: { postedById: true, title: true },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    // Check if user is the job poster or admin
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    if (job.postedById !== req.user.userId && user.role !== UserRole.ADMIN) {
      throw createError("Not authorized to delete this job", 403);
    }

    // Delete job (this will cascade delete applications)
    await prisma.job.delete({
      where: { id: id as string },
    });

    res.json({
      message: "Job deleted successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Apply for a job
 */
export const applyForJob = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;
    const { resumeUrl, message } = req.body as JobApplicationRequest;

    // Check if job exists and is active
    const job = await prisma.job.findUnique({
      where: { id: id as string },
      select: {
        id: true,
        title: true,
        company: true,
        isActive: true,
        expiresAt: true,
        allowResume: true,
        postedById: true,
      },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    if (!job.isActive) {
      throw createError("Job is no longer active", 410);
    }

    if (job.expiresAt && job.expiresAt < new Date()) {
      throw createError("Job has expired", 410);
    }

    // Check if user is trying to apply to their own job
    if (job.postedById === req.user.userId) {
      throw createError("Cannot apply to your own job posting", 400);
    }

    // Check if user has already applied
    const existingApplication = await prisma.jobApplication.findUnique({
      where: {
        jobId_applicantId: {
          jobId: id as string,
          applicantId: req.user.userId,
        },
      },
    });

    if (existingApplication) {
      throw createError("You have already applied for this job", 409);
    }

    // Validate resume requirement
    if (job.allowResume && !resumeUrl) {
      throw createError("Resume is required for this job application", 400);
    }

    // Create application
    const application = await prisma.jobApplication.create({
      data: {
        jobId: id as string,
        applicantId: req.user.userId,
        resumeUrl: resumeUrl ?? null,
        message: message ?? null,
      },
      include: {
        job: {
          select: {
            title: true,
            company: true,
          },
        },
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
            profilePicture: true,
          },
        },
      },
    });

    res.status(201).json({
      message: "Application submitted successfully",
      application: {
        id: application.id,
        job: {
          title: job.title,
          company: job.company,
        },
        status: application.status,
        createdAt: application.createdAt,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get job applications for a specific job (only job poster or admin)
 */
export const getJobApplications = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;
    const status = req.query.status as string;

    // Check if job exists and user has permission
    const job = await prisma.job.findUnique({
      where: { id: id as string },
      select: { postedById: true, title: true, company: true },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    // Check if user is the job poster or admin
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    if (job.postedById !== req.user.userId && user.role !== UserRole.ADMIN) {
      throw createError("Not authorized to view applications for this job", 403);
    }

    // Build where clause
    const where: any = { jobId: id };
    if (status) {
      where.status = status;
    }

    const [applications, total] = await Promise.all([
      prisma.jobApplication.findMany({
        where,
        include: {
          applicant: {
            select: {
              id: true,
              name: true,
              email: true,
              profilePicture: true,
              course: true,
              linkedinUrl: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.jobApplication.count({ where }),
    ]);

    res.json({
      job: {
        id,
        title: job.title,
        company: job.company,
      },
      applications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update application status (only job poster or admin)
 */
export const updateApplicationStatus = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { jobId, applicationId } = req.params;
    const { status } = req.body as ApplicationStatusRequest;

    // Check if job exists and user has permission
    const job = await prisma.job.findUnique({
      where: { id: jobId as string },
      select: { postedById: true },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    // Check if user is the job poster or admin
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    if (job.postedById !== req.user.userId && user.role !== UserRole.ADMIN) {
      throw createError("Not authorized to update application status", 403);
    }

    // Check if application exists
    const application = await prisma.jobApplication.findUnique({
      where: { id: applicationId as string },
      select: { jobId: true },
    });

    if (!application) {
      throw createError("Application not found", 404);
    }

    if (application.jobId !== jobId) {
      throw createError("Application does not belong to this job", 400);
    }

    // Update application status
    const updatedApplication = await prisma.jobApplication.update({
      where: { id: applicationId as string },
      data: { status },
      include: {
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
            profilePicture: true,
          },
        },
        job: {
          select: {
            title: true,
            company: true,
          },
        },
      },
    });

    res.json({
      message: "Application status updated successfully",
      application: updatedApplication,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get user's job applications
 */
export const getMyApplications = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;
    const status = req.query.status as string;

    // Build where clause
    const where: any = { applicantId: req.user.userId };
    if (status) {
      where.status = status;
    }

    const [applications, total] = await Promise.all([
      prisma.jobApplication.findMany({
        where,
        include: {
          job: {
            select: {
              id: true,
              title: true,
              company: true,
              location: true,
              type: true,
              isActive: true,
              expiresAt: true,
              postedBy: {
                select: {
                  name: true,
                  profilePicture: true,
                },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.jobApplication.count({ where }),
    ]);

    res.json({
      applications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

import { Request, Response, NextFunction } from "express";
import { UserRole, UserStatus } from "@prisma/client";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";

interface CreateEventRequest {
  title: string;
  description: string;
  location?: string;
  imageUrl?: string;
  startTime: string;
  endTime?: string;
  isOnline: boolean;
  meetingUrl?: string;
  maxAttendees?: number;
}

interface UpdateEventRequest {
  title?: string;
  description?: string;
  location?: string;
  imageUrl?: string;
  startTime?: string;
  endTime?: string;
  isOnline?: boolean;
  meetingUrl?: string;
  maxAttendees?: number;
  isActive?: boolean;
}

interface RSVPRequest {
  status: "GOING" | "MAYBE" | "NOT_GOING";
}

/**
 * Create a new event
 */
export const createEvent = async (req: Request<{}, {}, CreateEventRequest>, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { title, description, location, imageUrl, startTime, endTime, isOnline, meetingUrl, maxAttendees } = req.body;

    // Only alumni and admins can create events
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true, status: true },
    });

    if (!user || user.status !== UserStatus.APPROVED) {
      throw createError("User not found or not approved", 403);
    }

    if (user.role !== UserRole.ALUMNI && user.role !== UserRole.ADMIN) {
      throw createError("Only alumni and admins can create events", 403);
    }

    // Validate dates
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : null;

    if (start < new Date()) {
      throw createError("Event start time cannot be in the past", 400);
    }

    if (end && end <= start) {
      throw createError("Event end time must be after start time", 400);
    }

    // Validate online event requirements
    if (isOnline && !meetingUrl) {
      throw createError("Meeting URL is required for online events", 400);
    }

    // Create event
    const event = await prisma.event.create({
      data: {
        title,
        description,
        location: location ?? null,
        imageUrl: imageUrl ?? null,
        startTime: start,
        endTime: end,
        isOnline,
        meetingUrl: meetingUrl ?? null,
        maxAttendees: maxAttendees ?? null,
        organizerId: req.user.userId,
      },
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
          },
        },
        _count: {
          select: {
            rsvps: {
              where: { status: "GOING" },
            },
          },
        },
      },
    });

    res.status(201).json({
      message: "Event created successfully",
      event,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all events with filtering and pagination
 */
export const getEvents = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    // Filtering options
    const search = req.query.search as string;
    const isOnline = req.query.isOnline as string;
    const upcoming = req.query.upcoming as string;

    // Build where clause
    const where: any = {
      isActive: true,
    };

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { location: { contains: search, mode: "insensitive" } },
      ];
    }

    if (isOnline === "true") {
      where.isOnline = true;
    } else if (isOnline === "false") {
      where.isOnline = false;
    }

    if (upcoming === "true") {
      where.startTime = { gte: new Date() };
    }

    const [events, total] = await Promise.all([
      prisma.event.findMany({
        where,
        include: {
          organizer: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
              company: true,
              jobTitle: true,
            },
          },
          _count: {
            select: {
              rsvps: {
                where: { status: "GOING" },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: { startTime: "asc" },
      }),
      prisma.event.count({ where }),
    ]);

    res.json({
      events,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get event by ID
 */
export const getEventById = async (req: Request<{ id: string }>, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const event = await prisma.event.findUnique({
      where: { id },
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
            linkedinUrl: true,
          },
        },
        rsvps: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                profilePicture: true,
                role: true,
                company: true,
                jobTitle: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        },
        _count: {
          select: {
            rsvps: {
              where: { status: "GOING" },
            },
          },
        },
      },
    });

    if (!event) {
      throw createError("Event not found", 404);
    }

    if (!event.isActive) {
      throw createError("Event is no longer active", 410);
    }

    // Check user's RSVP status if authenticated
    let userRSVP = null;
    if (req.user) {
      userRSVP = await prisma.eventRSVP.findUnique({
        where: {
          eventId_userId: {
            eventId: id,
            userId: req.user.userId,
          },
        },
      });
    }

    res.json({
      event: {
        ...event,
        userRSVP: userRSVP?.status || null,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update event (only by event organizer or admin)
 */
export const updateEvent = async (
  req: Request<{ id: string }, {}, UpdateEventRequest>,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;
    const updateData = req.body;

    // Find event and check ownership
    const event = await prisma.event.findUnique({
      where: { id },
      select: { organizerId: true, startTime: true },
    });

    if (!event) {
      throw createError("Event not found", 404);
    }

    // Check if user is the event organizer or admin
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    if (event.organizerId !== req.user.userId && user.role !== UserRole.ADMIN) {
      throw createError("Not authorized to update this event", 403);
    }

    // Validate dates if provided
    if (updateData.startTime) {
      const start = new Date(updateData.startTime);
      if (start < new Date()) {
        throw createError("Event start time cannot be in the past", 400);
      }
    }

    if (updateData.endTime && updateData.startTime) {
      const start = new Date(updateData.startTime);
      const end = new Date(updateData.endTime);
      if (end <= start) {
        throw createError("Event end time must be after start time", 400);
      }
    }

    // Prepare update data
    const cleanedData: any = Object.fromEntries(Object.entries(updateData).filter(([_, value]) => value !== undefined));

    if (cleanedData.startTime) {
      cleanedData.startTime = new Date(cleanedData.startTime);
    }

    if (cleanedData.endTime) {
      cleanedData.endTime = new Date(cleanedData.endTime);
    }

    const updatedEvent = await prisma.event.update({
      where: { id },
      data: cleanedData,
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
          },
        },
        _count: {
          select: {
            rsvps: {
              where: { status: "GOING" },
            },
          },
        },
      },
    });

    res.json({
      message: "Event updated successfully",
      event: updatedEvent,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete event (only by event organizer or admin)
 */
export const deleteEvent = async (req: Request<{ id: string }>, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;

    // Find event and check ownership
    const event = await prisma.event.findUnique({
      where: { id },
      select: { organizerId: true, title: true },
    });

    if (!event) {
      throw createError("Event not found", 404);
    }

    // Check if user is the event organizer or admin
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    if (event.organizerId !== req.user.userId && user.role !== UserRole.ADMIN) {
      throw createError("Not authorized to delete this event", 403);
    }

    // Delete event (this will cascade delete RSVPs)
    await prisma.event.delete({
      where: { id },
    });

    res.json({
      message: "Event deleted successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * RSVP to an event
 */
export const rsvpToEvent = async (req: Request<{ id: string }, {}, RSVPRequest>, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;
    const { status } = req.body;

    // Check if event exists and is active
    const event = await prisma.event.findUnique({
      where: { id },
      select: {
        id: true,
        title: true,
        isActive: true,
        startTime: true,
        maxAttendees: true,
        organizerId: true,
        _count: {
          select: {
            rsvps: {
              where: { status: "GOING" },
            },
          },
        },
      },
    });

    if (!event) {
      throw createError("Event not found", 404);
    }

    if (!event.isActive) {
      throw createError("Event is no longer active", 410);
    }

    if (event.startTime < new Date()) {
      throw createError("Cannot RSVP to past events", 400);
    }

    // Check if event is full (only for GOING status)
    if (status === "GOING" && event.maxAttendees && event._count.rsvps >= event.maxAttendees) {
      throw createError("Event is full", 409);
    }

    // Create or update RSVP
    const rsvp = await prisma.eventRSVP.upsert({
      where: {
        eventId_userId: {
          eventId: id,
          userId: req.user.userId,
        },
      },
      update: { status },
      create: {
        eventId: id,
        userId: req.user.userId,
        status,
      },
      include: {
        event: {
          select: {
            title: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Create notification for event organizer (only for GOING status)
    if (status === "GOING" && event.organizerId !== req.user.userId) {
      await prisma.notification.create({
        data: {
          userId: event.organizerId,
          type: "EVENT_CREATED",
          title: "New Event RSVP",
          message: `${rsvp.user.name} will attend your event: ${event.title}`,
          data: {
            eventId: id,
            rsvpId: rsvp.id,
            userId: req.user.userId,
          },
        },
      });
    }

    res.json({
      message: `RSVP updated to ${status}`,
      rsvp: {
        id: rsvp.id,
        status: rsvp.status,
        event: rsvp.event,
        createdAt: rsvp.createdAt,
        updatedAt: rsvp.updatedAt,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get user's event RSVPs
 */
export const getMyRSVPs = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;
    const status = req.query.status as string;
    const upcoming = req.query.upcoming as string;

    // Build where clause
    const where: any = { userId: req.user.userId };
    if (status) {
      where.status = status;
    }

    // Add event filter for upcoming events
    if (upcoming === "true") {
      where.event = {
        startTime: { gte: new Date() },
        isActive: true,
      };
    }

    const [rsvps, total] = await Promise.all([
      prisma.eventRSVP.findMany({
        where,
        include: {
          event: {
            select: {
              id: true,
              title: true,
              description: true,
              location: true,
              imageUrl: true,
              startTime: true,
              endTime: true,
              isOnline: true,
              meetingUrl: true,
              isActive: true,
              organizer: {
                select: {
                  id: true,
                  name: true,
                  profilePicture: true,
                },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: { event: { startTime: "asc" } },
      }),
      prisma.eventRSVP.count({ where }),
    ]);

    res.json({
      rsvps,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
